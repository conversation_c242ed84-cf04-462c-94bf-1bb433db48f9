/**
 * GoogleMapsLoader Component
 *
 * A wrapper component that ensures Google Maps API is loaded before rendering map components.
 * Provides loading states and error handling for Google Maps integration.
 */

"use client";

import React, { useState, useEffect } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Loader2 } from "lucide-react";

interface GoogleMapsLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const GoogleMapsLoader: React.FC<GoogleMapsLoaderProps> = ({
  children,
  fallback,
}) => {
  const { isRTL } = useLanguage();
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadGoogleMaps = () => {
      // Check if already loaded
      if (window.google && window.google.maps) {
        setIsLoaded(true);
        setIsLoading(false);
        return;
      }

      // Check if script is already in the document
      const existingScript = document.querySelector(
        'script[src*="maps.googleapis.com"]'
      );
      if (existingScript) {
        // Script exists, wait for it to load
        const checkInterval = setInterval(() => {
          if (window.google && window.google.maps) {
            setIsLoaded(true);
            setIsLoading(false);
            clearInterval(checkInterval);
          }
        }, 100);

        const timeout = setTimeout(() => {
          if (!window.google || !window.google.maps) {
            setHasError(true);
            setIsLoading(false);
            clearInterval(checkInterval);
          }
        }, 10000);

        return () => {
          clearInterval(checkInterval);
          clearTimeout(timeout);
        };
      }

      // Load Google Maps API
      const script = document.createElement("script");
      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

      if (!apiKey || apiKey === "YOUR_GOOGLE_MAPS_API_KEY_HERE") {
        console.log("Google Maps API key not configured, using fallback map");
        setHasError(true);
        setIsLoading(false);
        return;
      }

      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,geometry&callback=initMap`;
      script.async = true;
      script.defer = true;

      // Create a global callback function
      (window as any).initMap = () => {
        console.log("Google Maps API loaded successfully via callback");
        setIsLoaded(true);
        setIsLoading(false);
        delete (window as any).initMap; // Clean up
      };

      script.onload = () => {
        console.log("Google Maps script loaded");
      };

      script.onerror = (error) => {
        console.error("Failed to load Google Maps API:", error);
        setHasError(true);
        setIsLoading(false);
        delete (window as any).initMap; // Clean up
      };

      document.head.appendChild(script);
    };

    loadGoogleMaps();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 font-medium">
            {isRTL ? "جاري تحميل الخريطة..." : "Loading map..."}
          </p>
          <p className="text-sm text-gray-500 mt-2">
            {isRTL ? "يرجى الانتظار..." : "Please wait..."}
          </p>
        </div>
      </div>
    );
  }

  if (hasError) {
    // Return fallback directly without showing error message
    return fallback || null;
  }

  if (isLoaded) {
    return <>{children}</>;
  }

  return null;
};

export default GoogleMapsLoader;
