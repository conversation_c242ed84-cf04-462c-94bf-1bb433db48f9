/**
 * GoogleMapsLoader Component
 *
 * A wrapper component that ensures Google Maps API is loaded before rendering map components.
 * Provides loading states and error handling for Google Maps integration.
 */

"use client";

import React, { useState, useEffect } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Loader2, AlertCircle } from "lucide-react";

interface GoogleMapsLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const GoogleMapsLoader: React.FC<GoogleMapsLoaderProps> = ({
  children,
  fallback,
}) => {
  const { isRTL } = useLanguage();
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadGoogleMaps = () => {
      // Check if already loaded
      if (window.google && window.google.maps) {
        setIsLoaded(true);
        setIsLoading(false);
        return;
      }

      // Check if script is already in the document
      const existingScript = document.querySelector(
        'script[src*="maps.googleapis.com"]'
      );
      if (existingScript) {
        // Script exists, wait for it to load
        const checkInterval = setInterval(() => {
          if (window.google && window.google.maps) {
            setIsLoaded(true);
            setIsLoading(false);
            clearInterval(checkInterval);
          }
        }, 100);

        const timeout = setTimeout(() => {
          if (!window.google || !window.google.maps) {
            setHasError(true);
            setIsLoading(false);
            clearInterval(checkInterval);
          }
        }, 10000);

        return () => {
          clearInterval(checkInterval);
          clearTimeout(timeout);
        };
      }

      // Load Google Maps API
      const script = document.createElement("script");
      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

      if (!apiKey || apiKey === "YOUR_GOOGLE_MAPS_API_KEY_HERE") {
        console.error("Google Maps API key is missing or invalid");
        setHasError(true);
        setIsLoading(false);
        return;
      }

      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,geometry&callback=initMap`;
      script.async = true;
      script.defer = true;

      // Create a global callback function
      (window as any).initMap = () => {
        console.log("Google Maps API loaded successfully via callback");
        setIsLoaded(true);
        setIsLoading(false);
        delete (window as any).initMap; // Clean up
      };

      script.onload = () => {
        console.log("Google Maps script loaded");
      };

      script.onerror = (error) => {
        console.error("Failed to load Google Maps API:", error);
        setHasError(true);
        setIsLoading(false);
        delete (window as any).initMap; // Clean up
      };

      document.head.appendChild(script);
    };

    loadGoogleMaps();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 font-medium">
            {isRTL ? "جاري تحميل الخريطة..." : "Loading map..."}
          </p>
          <p className="text-sm text-gray-500 mt-2">
            {isRTL ? "يرجى الانتظار..." : "Please wait..."}
          </p>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      fallback || (
        <div className="flex items-center justify-center h-96 bg-red-50 rounded-lg border border-red-200">
          <div className="text-center max-w-md">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">
              {isRTL ? "خطأ في تحميل الخريطة" : "Map Loading Error"}
            </h3>
            <p className="text-red-600 mb-4 text-sm">
              {isRTL
                ? "لا يمكن تحميل خدمة خرائط جوجل. يرجى التحقق من مفتاح API أو الاتصال بالإنترنت."
                : "Unable to load Google Maps service. Please check your API key or internet connection."}
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
              <p className="text-yellow-800 text-xs">
                {isRTL
                  ? "تحتاج إلى مفتاح Google Maps API صالح في ملف .env.local"
                  : "You need a valid Google Maps API key in .env.local file"}
              </p>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors text-sm"
            >
              {isRTL ? "إعادة تحميل" : "Reload"}
            </button>
          </div>
        </div>
      )
    );
  }

  if (isLoaded) {
    return <>{children}</>;
  }

  return null;
};

export default GoogleMapsLoader;
