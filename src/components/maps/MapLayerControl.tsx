/**
 * MapLayerControl Component
 *
 * A control panel for toggling different map layers on/off.
 * Provides a clean interface for managing map visibility.
 */

"use client";

import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { 
  Plane, 
  Anchor, 
  Shield, 
  MapPin, 
  Car, 
  Ship,
  Eye,
  EyeOff 
} from "lucide-react";

interface MapLayerControlProps {
  showAirports: boolean;
  showSeaports: boolean;
  showPoliceStations: boolean;
  showCheckpoints: boolean;
  showVehicles: boolean;
  showVessels: boolean;
  showPorts: boolean;
  onToggleAirports: (show: boolean) => void;
  onToggleSeaports: (show: boolean) => void;
  onTogglePoliceStations: (show: boolean) => void;
  onToggleCheckpoints: (show: boolean) => void;
  onToggleVehicles: (show: boolean) => void;
  onToggleVessels: (show: boolean) => void;
  onTogglePorts: (show: boolean) => void;
}

const MapLayerControl: React.FC<MapLayerControlProps> = ({
  showAirports,
  showSeaports,
  showPoliceStations,
  showCheckpoints,
  showVehicles,
  showVessels,
  showPorts,
  onToggleAirports,
  onToggleSeaports,
  onTogglePoliceStations,
  onToggleCheckpoints,
  onToggleVehicles,
  onToggleVessels,
  onTogglePorts,
}) => {
  const { isRTL } = useLanguage();

  const layers = [
    {
      key: "airports",
      label: isRTL ? "المطارات" : "Airports",
      icon: Plane,
      isVisible: showAirports,
      onToggle: onToggleAirports,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      key: "seaports",
      label: isRTL ? "الموانئ البحرية" : "Seaports",
      icon: Anchor,
      isVisible: showSeaports,
      onToggle: onToggleSeaports,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      key: "ports",
      label: isRTL ? "الموانئ" : "Ports",
      icon: MapPin,
      isVisible: showPorts,
      onToggle: onTogglePorts,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
    },
    {
      key: "police",
      label: isRTL ? "مراكز الشرطة" : "Police Stations",
      icon: Shield,
      isVisible: showPoliceStations,
      onToggle: onTogglePoliceStations,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
    },
    {
      key: "checkpoints",
      label: isRTL ? "نقاط التفتيش" : "Checkpoints",
      icon: MapPin,
      isVisible: showCheckpoints,
      onToggle: onToggleCheckpoints,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      key: "vehicles",
      label: isRTL ? "المركبات" : "Vehicles",
      icon: Car,
      isVisible: showVehicles,
      onToggle: onToggleVehicles,
      color: "text-gray-600",
      bgColor: "bg-gray-50",
    },
    {
      key: "vessels",
      label: isRTL ? "السفن" : "Vessels",
      icon: Ship,
      isVisible: showVessels,
      onToggle: onToggleVessels,
      color: "text-cyan-600",
      bgColor: "bg-cyan-50",
    },
  ];

  return (
    <div className="absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 p-4 z-10 max-w-xs">
      <h3 className="font-bold text-gray-800 mb-3 flex items-center gap-2">
        <Eye className="w-5 h-5 text-blue-600" />
        {isRTL ? "طبقات الخريطة" : "Map Layers"}
      </h3>
      
      <div className="space-y-2">
        {layers.map((layer) => {
          const IconComponent = layer.icon;
          return (
            <button
              key={layer.key}
              onClick={() => layer.onToggle(!layer.isVisible)}
              className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                layer.isVisible
                  ? `${layer.bgColor} border-2 border-current ${layer.color}`
                  : "bg-gray-50 border-2 border-gray-200 text-gray-500 hover:bg-gray-100"
              }`}
            >
              <div className={`p-1 rounded ${layer.isVisible ? layer.bgColor : "bg-gray-100"}`}>
                <IconComponent className={`w-4 h-4 ${layer.isVisible ? layer.color : "text-gray-400"}`} />
              </div>
              
              <span className="text-sm font-medium flex-1 text-left">
                {layer.label}
              </span>
              
              {layer.isVisible ? (
                <Eye className="w-4 h-4" />
              ) : (
                <EyeOff className="w-4 h-4" />
              )}
            </button>
          );
        })}
      </div>
      
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="flex justify-between text-xs text-gray-500">
          <span>{isRTL ? "المجموع:" : "Total:"}</span>
          <span>{layers.filter(l => l.isVisible).length}/{layers.length}</span>
        </div>
      </div>
    </div>
  );
};

export default MapLayerControl;
