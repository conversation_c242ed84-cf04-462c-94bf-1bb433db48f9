/**
 * InteractiveMap Component
 *
 * A comprehensive interactive map component built with Google Maps for real-time tracking
 * and monitoring of maritime and Saudi Arabian locations. Supports multiple data types,
 * custom markers, multilingual content, and real-time visualization.
 *
 * Features:
 * - Multi-layer Support: Airports, seaports, police stations, checkpoints, vehicles, ports, vessels
 * - Custom Markers: Unique icons for each data type with status-based styling
 * - Multilingual Popups: Automatic Arabic/English content switching
 * - RTL Support: Proper right-to-left layout support
 * - Real-time Updates: Support for live data updates
 * - Event Handling: Click handlers for all marker types
 * - Responsive Design: Works on all screen sizes
 *
 * @example
 * ```tsx
 * <InteractiveMap
 *   center={[24.7136, 46.6753]} // Riyadh coordinates
 *   zoom={6}
 *   height="100vh"
 *   airports={saudiLocations.airports}
 *   vehicles={saudiVehicles}
 *   showAirports={true}
 *   showVehicles={true}
 *   onAirportClick={(airport) => console.log('Airport clicked:', airport.name)}
 *   onVehicleClick={(vehicle) => console.log('Vehicle clicked:', vehicle.plateNumber)}
 * />
 * ```
 *
 */

"use client";

import React, { useEffect, useState, useMemo } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Loader2 } from "lucide-react";
import GoogleMapWrapper, { GoogleMapMarker } from "./GoogleMapWrapper";
import FallbackMap from "./FallbackMap";
import GoogleMapsLoader from "./GoogleMapsLoader";

// Note: Saudi data can be imported when needed:
// import saudiLocations from "@/data/saudi-locations.json";
// import saudiVehicles from "@/data/saudi-vehicles.json";

// Types for Saudi locations and vehicles
export interface Airport {
  id: string;
  name: string;
  nameEn: string;
  coordinates: [number, number];
  city: string;
  cityEn: string;
  type: "international" | "domestic";
  status: string;
  iata: string;
  icao: string;
}

export interface Seaport {
  id: string;
  name: string;
  nameEn: string;
  coordinates: [number, number];
  city: string;
  cityEn: string;
  type: string;
  status: string;
  capacity: number;
  vessels: number;
}

export interface PoliceStation {
  id: string;
  name: string;
  nameEn: string;
  coordinates: [number, number];
  city: string;
  cityEn: string;
  type: string;
  status: string;
}

export interface Checkpoint {
  id: string;
  name: string;
  nameEn: string;
  coordinates: [number, number];
  highway: string;
  highwayEn: string;
  type: string;
  status: string;
}

export interface Vehicle {
  id: string;
  name: string;
  nameEn: string;
  coordinates: [number, number];
  type: string;
  status: string;
  speed: number;
  heading: number;
  destination: string;
  destinationEn: string;
  origin: string;
  originEn: string;
  plateNumber: string;
  driver?: string;
  driverEn?: string;
  officer?: string;
  officerEn?: string;
  cargo?: string;
  cargoEn?: string;
  passengers?: number;
  priority?: string;
}

// Legacy types for backward compatibility
export interface Port extends Seaport {
  country: string;
  countryAr: string;
}

export interface Vessel extends Vehicle {
  flag: string;
  flagAr: string;
  lastUpdate: string;
}

interface InteractiveMapProps {
  // Legacy props for backward compatibility
  ports?: Port[];
  vessels?: Vessel[];
  // New Saudi-specific props
  airports?: Airport[];
  seaports?: Seaport[];
  policeStations?: PoliceStation[];
  checkpoints?: Checkpoint[];
  vehicles?: Vehicle[];
  // Common props
  center?: [number, number];
  zoom?: number;
  height?: string;
  // Display toggles
  showPorts?: boolean;
  showVessels?: boolean;
  showAirports?: boolean;
  showSeaports?: boolean;
  showPoliceStations?: boolean;
  showCheckpoints?: boolean;
  showVehicles?: boolean;
  // Event handlers
  onPortClick?: (port: Port) => void;
  onVesselClick?: (vessel: Vessel) => void;
  onAirportClick?: (airport: Airport) => void;
  onSeaportClick?: (seaport: Seaport) => void;
  onPoliceStationClick?: (station: PoliceStation) => void;
  onCheckpointClick?: (checkpoint: Checkpoint) => void;
  onVehicleClick?: (vehicle: Vehicle) => void;
}

const InteractiveMap: React.FC<InteractiveMapProps> = ({
  // Legacy props
  ports = [],
  vessels = [],
  // New Saudi props
  airports = [],
  seaports = [],
  policeStations = [],
  checkpoints = [],
  vehicles = [],
  // Common props
  center = [24.7136, 46.6753], // Riyadh coordinates as default
  zoom = 6,
  height = "600px",
  // Display toggles
  showPorts = true,
  showVessels = true,
  showAirports = true,
  showSeaports = true,
  showPoliceStations = true,
  showCheckpoints = true,
  showVehicles = true,
  // Event handlers
  onPortClick,
  onVesselClick,
  onAirportClick,
  onSeaportClick,
  onPoliceStationClick,
  onCheckpointClick,
  onVehicleClick,
}) => {
  const { isRTL } = useLanguage();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Create custom SVG marker icons for different types
  const createSVGMarker = (type: string, status: string) => {
    const iconConfigs = {
      airport: {
        color: status === "active" ? "#2563eb" : "#ef4444", // Blue for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
        </svg>`,
      },
      seaport: {
        color: status === "active" ? "#059669" : "#ef4444", // Green for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
        </svg>`,
      },
      port: {
        color: status === "active" ? "#059669" : "#ef4444", // Green for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M20 21c-1.39 0-2.78-.47-4-1.32-2.44 1.71-5.56 1.71-8 0C6.78 20.53 5.39 21 4 21H2v2h2c1.38 0 2.74-.35 4-.99 2.52 1.29 5.48 1.29 8 0 1.26.65 2.62.99 4 .99h2v-2h-2zM3.95 19H4c1.6 0 3.02-.88 4-2 .98 1.12 2.4 2 4 2s3.02-.88 4-2c.98 1.12 2.4 2 4 2h.05l1.89-6.68c.08-.26.06-.54-.06-.78s-.32-.42-.58-.5L20 10.62V6c0-1.1-.9-2-2-2h-3V1H9v3H6c-1.1 0-2 .9-2 2v4.62l-1.29.42c-.26.08-.46.26-.58.5s-.14.52-.06.78L3.95 19z"/>
        </svg>`,
      },
      police: {
        color: status === "active" ? "#1e40af" : "#ef4444", // Dark blue for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
        </svg>`,
      },
      police_station: {
        color: status === "active" ? "#1e40af" : "#ef4444", // Dark blue for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
        </svg>`,
      },
      checkpoint: {
        color: status === "active" ? "#ea580c" : "#ef4444", // Orange for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm3 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/>
        </svg>`,
      },
      vehicle: {
        color: status === "active" ? "#4b5563" : "#ef4444", // Gray for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/>
        </svg>`,
      },
      vessel: {
        color: status === "active" ? "#0891b2" : "#ef4444", // Cyan for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M20 21c-1.39 0-2.78-.47-4-1.32-2.44 1.71-5.56 1.71-8 0C6.78 20.53 5.39 21 4 21H2v2h2c1.38 0 2.74-.35 4-.99 2.52 1.29 5.48 1.29 8 0 1.26.65 2.62.99 4 .99h2v-2h-2zM3.95 19H4c1.6 0 3.02-.88 4-2 .98 1.12 2.4 2 4 2s3.02-.88 4-2c.98 1.12 2.4 2 4 2h.05l1.89-6.68c.08-.26.06-.54-.06-.78s-.32-.42-.58-.5L20 10.62V6c0-1.1-.9-2-2-2h-3V1H9v3H6c-1.1 0-2 .9-2 2v4.62l-1.29.42c-.26.08-.46.26-.58.5s-.14.52-.06.78L3.95 19z"/>
        </svg>`,
      },
      ship: {
        color: status === "active" ? "#0891b2" : "#ef4444", // Cyan for active, red for inactive
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M20 21c-1.39 0-2.78-.47-4-1.32-2.44 1.71-5.56 1.71-8 0C6.78 20.53 5.39 21 4 21H2v2h2c1.38 0 2.74-.35 4-.99 2.52 1.29 5.48 1.29 8 0 1.26.65 2.62.99 4 .99h2v-2h-2zM3.95 19H4c1.6 0 3.02-.88 4-2 .98 1.12 2.4 2 4 2s3.02-.88 4-2c.98 1.12 2.4 2 4 2h.05l1.89-6.68c.08-.26.06-.54-.06-.78s-.32-.42-.58-.5L20 10.62V6c0-1.1-.9-2-2-2h-3V1H9v3H6c-1.1 0-2 .9-2 2v4.62l-1.29.42c-.26.08-.46.26-.58.5s-.14.52-.06.78L3.95 19z"/>
        </svg>`,
      },
    };

    const config = iconConfigs[type as keyof typeof iconConfigs] || {
      color: status === "active" ? "#6b7280" : "#ef4444",
      svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
      </svg>`,
    };

    // Check if Google Maps is available
    if (typeof window !== "undefined" && window.google && window.google.maps) {
      return {
        url:
          "data:image/svg+xml;charset=UTF-8," +
          encodeURIComponent(`
          <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
            <circle cx="20" cy="20" r="18" fill="${config.color}" stroke="white" stroke-width="2"/>
            <g transform="translate(8, 8) scale(0.5)">
              ${config.svg}
            </g>
          </svg>
        `),
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 20),
      };
    }

    // Fallback: return just the SVG URL for non-Google Maps usage
    return (
      "data:image/svg+xml;charset=UTF-8," +
      encodeURIComponent(`
        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="20" r="18" fill="${config.color}" stroke="white" stroke-width="2"/>
          <g transform="translate(8, 8) scale(0.5)">
            ${config.svg}
          </g>
        </svg>
      `)
    );
  };

  // Convert coordinates from [lat, lng] to {lat, lng} format for Google Maps
  const convertCoordinates = (
    coords: [number, number]
  ): { lat: number; lng: number } => ({
    lat: coords[0],
    lng: coords[1],
  });

  // Create markers array for Google Maps
  const createMarkersArray = useMemo(() => {
    const markers: GoogleMapMarker[] = [];

    // Add port markers
    if (showPorts) {
      ports.forEach((port) => {
        const infoContent = `
          <div style="direction: ${
            isRTL ? "rtl" : "ltr"
          }; font-family: system-ui, sans-serif; max-width: 250px;">
            <h3 style="margin: 0 0 8px 0; color: #002447; font-size: 16px; font-weight: bold;">
              ${isRTL ? port.name : port.nameEn}
            </h3>
            <div style="font-size: 14px; line-height: 1.4;">
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "النوع:" : "Type:"
              }</strong> ${port.type}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "الحالة:" : "Status:"
              }</strong>
                <span style="color: ${
                  port.status === "active" ? "#10b981" : "#ef4444"
                };">${port.status}</span>
              </p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "السفن:" : "Vessels:"
              }</strong> ${port.vessels}/${port.capacity}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "البلد:" : "Country:"
              }</strong> ${isRTL ? port.countryAr : port.country}</p>
            </div>
          </div>
        `;

        markers.push({
          id: `port-${port.id}`,
          position: convertCoordinates(port.coordinates),
          title: isRTL ? port.name : port.nameEn,
          icon: createSVGMarker("port", port.status),
          onClick: () => onPortClick?.(port),
          infoWindow: {
            content: infoContent,
            isRTL: isRTL,
          },
          type: "port",
          status: port.status,
        });
      });
    }

    // Add vessel markers
    if (showVessels) {
      vessels.forEach((vessel) => {
        markers.push({
          id: `vessel-${vessel.id}`,
          position: convertCoordinates(vessel.coordinates),
          title: isRTL ? vessel.name : vessel.nameEn,
          icon: createSVGMarker("vessel", vessel.status),
          onClick: () => onVesselClick?.(vessel),
        });
      });
    }

    // Add airport markers
    if (showAirports) {
      airports.forEach((airport) => {
        const infoContent = `
          <div style="direction: ${
            isRTL ? "rtl" : "ltr"
          }; font-family: system-ui, sans-serif; max-width: 250px;">
            <h3 style="margin: 0 0 8px 0; color: #002447; font-size: 16px; font-weight: bold;">
              ${isRTL ? airport.name : airport.nameEn}
            </h3>
            <div style="font-size: 14px; line-height: 1.4;">
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "المدينة:" : "City:"
              }</strong> ${isRTL ? airport.city : airport.cityEn}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "النوع:" : "Type:"
              }</strong> ${airport.type}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "رمز IATA:" : "IATA:"
              }</strong> ${airport.iata}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "رمز ICAO:" : "ICAO:"
              }</strong> ${airport.icao}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "الحالة:" : "Status:"
              }</strong>
                <span style="color: ${
                  airport.status === "active" ? "#10b981" : "#ef4444"
                };">${airport.status}</span>
              </p>
            </div>
          </div>
        `;

        markers.push({
          id: `airport-${airport.id}`,
          position: convertCoordinates(airport.coordinates),
          title: isRTL ? airport.name : airport.nameEn,
          icon: createSVGMarker("airport", airport.status),
          onClick: () => onAirportClick?.(airport),
          infoWindow: {
            content: infoContent,
            isRTL: isRTL,
          },
          type: "airport",
          status: airport.status,
        });
      });
    }

    // Add seaport markers
    if (showSeaports) {
      seaports.forEach((seaport) => {
        const infoContent = `
          <div style="direction: ${
            isRTL ? "rtl" : "ltr"
          }; font-family: system-ui, sans-serif; max-width: 250px;">
            <h3 style="margin: 0 0 8px 0; color: #002447; font-size: 16px; font-weight: bold;">
              ${isRTL ? seaport.name : seaport.nameEn}
            </h3>
            <div style="font-size: 14px; line-height: 1.4;">
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "المدينة:" : "City:"
              }</strong> ${isRTL ? seaport.city : seaport.cityEn}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "النوع:" : "Type:"
              }</strong> ${seaport.type}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "الحالة:" : "Status:"
              }</strong>
                <span style="color: ${
                  seaport.status === "active" ? "#10b981" : "#ef4444"
                };">${seaport.status}</span>
              </p>
            </div>
          </div>
        `;

        markers.push({
          id: `seaport-${seaport.id}`,
          position: convertCoordinates(seaport.coordinates),
          title: isRTL ? seaport.name : seaport.nameEn,
          icon: createSVGMarker("seaport", seaport.status),
          onClick: () => onSeaportClick?.(seaport),
          infoWindow: {
            content: infoContent,
            isRTL: isRTL,
          },
          type: "seaport",
          status: seaport.status,
        });
      });
    }

    // Add police station markers
    if (showPoliceStations) {
      policeStations.forEach((station) => {
        const infoContent = `
          <div style="direction: ${
            isRTL ? "rtl" : "ltr"
          }; font-family: system-ui, sans-serif; max-width: 250px;">
            <h3 style="margin: 0 0 8px 0; color: #002447; font-size: 16px; font-weight: bold;">
              ${isRTL ? station.name : station.nameEn}
            </h3>
            <div style="font-size: 14px; line-height: 1.4;">
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "المدينة:" : "City:"
              }</strong> ${isRTL ? station.city : station.cityEn}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "النوع:" : "Type:"
              }</strong> ${station.type}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "الحالة:" : "Status:"
              }</strong>
                <span style="color: ${
                  station.status === "active" ? "#10b981" : "#ef4444"
                };">${station.status}</span>
              </p>
            </div>
          </div>
        `;

        markers.push({
          id: `police-${station.id}`,
          position: convertCoordinates(station.coordinates),
          title: isRTL ? station.name : station.nameEn,
          icon: createSVGMarker("police_station", station.status),
          onClick: () => onPoliceStationClick?.(station),
          infoWindow: {
            content: infoContent,
            isRTL: isRTL,
          },
          type: "police",
          status: station.status,
        });
      });
    }

    // Add checkpoint markers
    if (showCheckpoints) {
      checkpoints.forEach((checkpoint) => {
        const infoContent = `
          <div style="direction: ${
            isRTL ? "rtl" : "ltr"
          }; font-family: system-ui, sans-serif; max-width: 250px;">
            <h3 style="margin: 0 0 8px 0; color: #002447; font-size: 16px; font-weight: bold;">
              ${isRTL ? checkpoint.name : checkpoint.nameEn}
            </h3>
            <div style="font-size: 14px; line-height: 1.4;">
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "النوع:" : "Type:"
              }</strong> ${checkpoint.type}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "النوع:" : "Type:"
              }</strong> ${checkpoint.type}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "الحالة:" : "Status:"
              }</strong>
                <span style="color: ${
                  checkpoint.status === "active" ? "#10b981" : "#ef4444"
                };">${checkpoint.status}</span>
              </p>
            </div>
          </div>
        `;

        markers.push({
          id: `checkpoint-${checkpoint.id}`,
          position: convertCoordinates(checkpoint.coordinates),
          title: isRTL ? checkpoint.name : checkpoint.nameEn,
          icon: createSVGMarker("checkpoint", checkpoint.status),
          onClick: () => onCheckpointClick?.(checkpoint),
          infoWindow: {
            content: infoContent,
            isRTL: isRTL,
          },
          type: "checkpoint",
          status: checkpoint.status,
        });
      });
    }

    // Add vehicle markers
    if (showVehicles) {
      vehicles.forEach((vehicle) => {
        const infoContent = `
          <div style="direction: ${
            isRTL ? "rtl" : "ltr"
          }; font-family: system-ui, sans-serif; max-width: 250px;">
            <h3 style="margin: 0 0 8px 0; color: #002447; font-size: 16px; font-weight: bold;">
              ${isRTL ? vehicle.name : vehicle.nameEn}
            </h3>
            <div style="font-size: 14px; line-height: 1.4;">
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "النوع:" : "Type:"
              }</strong> ${vehicle.type}</p>
              <p style="margin: 4px 0;"><strong>${
                isRTL ? "الحالة:" : "Status:"
              }</strong>
                <span style="color: ${
                  vehicle.status === "active" ? "#10b981" : "#ef4444"
                };">${vehicle.status}</span>
              </p>
            </div>
          </div>
        `;

        markers.push({
          id: `vehicle-${vehicle.id}`,
          position: convertCoordinates(vehicle.coordinates),
          title: isRTL ? vehicle.name : vehicle.nameEn,
          icon: createSVGMarker("vehicle", vehicle.status),
          onClick: () => onVehicleClick?.(vehicle),
          infoWindow: {
            content: infoContent,
            isRTL: isRTL,
          },
          type: "vehicle",
          status: vehicle.status,
        });
      });
    }

    return markers;
  }, [
    ports,
    vessels,
    airports,
    seaports,
    policeStations,
    checkpoints,
    vehicles,
    showPorts,
    showVessels,
    showAirports,
    showSeaports,
    showPoliceStations,
    showCheckpoints,
    showVehicles,
    isRTL,
    onPortClick,
    onVesselClick,
    onAirportClick,
    onSeaportClick,
    onPoliceStationClick,
    onCheckpointClick,
    onVehicleClick,
  ]);

  if (!isClient) {
    return (
      <div
        className="flex items-center justify-center bg-gray-100 rounded-lg"
        style={{ height }}
      >
        <div className="flex items-center gap-2 text-gray-600">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>{isRTL ? "جاري تحميل الخريطة..." : "Loading map..."}</span>
        </div>
      </div>
    );
  }

  return (
    <GoogleMapsLoader
      fallback={
        <FallbackMap
          center={convertCoordinates(center)}
          zoom={zoom}
          height={height}
          markers={createMarkersArray}
          className="rounded-lg overflow-hidden shadow-lg"
        />
      }
    >
      <GoogleMapWrapper
        center={convertCoordinates(center)}
        zoom={zoom}
        height={height}
        markers={createMarkersArray}
        className="rounded-lg overflow-hidden shadow-lg"
      />
    </GoogleMapsLoader>
  );
};

export default InteractiveMap;
