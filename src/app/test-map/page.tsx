"use client";

import React, { useState } from "react";
import InteractiveMap from "@/components/maps/InteractiveMap";
import { useLanguage } from "@/contexts/LanguageContext";

// Sample data for testing
const sampleAirports = [
  {
    id: "1",
    name: "King Khalid International Airport",
    nameAr: "مطار الملك خالد الدولي",
    location: [24.9576, 46.6988] as [number, number],
    status: "active",
    type: "international",
  },
  {
    id: "2", 
    name: "King Abdulaziz International Airport",
    nameAr: "مطار الملك عبدالعزيز الدولي",
    location: [21.6796, 39.1565] as [number, number],
    status: "active",
    type: "international",
  },
];

const sampleSeaports = [
  {
    id: "1",
    name: "King Abdulaziz Port",
    nameAr: "ميناء الملك عبدالعزيز",
    location: [21.4858, 39.1925] as [number, number],
    status: "active",
    type: "commercial",
  },
  {
    id: "2",
    name: "King Fahd Industrial Port",
    nameAr: "ميناء الملك فهد الصناعي",
    location: [27.0174, 49.9777] as [number, number],
    status: "active",
    type: "industrial",
  },
];

const sampleVehicles = [
  {
    id: "1",
    plateNumber: "ABC-123",
    plateNumberAr: "أ ب ج-123",
    location: [24.7136, 46.6753] as [number, number],
    status: "moving",
    speed: 65,
    direction: "north",
  },
  {
    id: "2",
    plateNumber: "XYZ-456", 
    plateNumberAr: "س ص ع-456",
    location: [21.4225, 39.8262] as [number, number],
    status: "stopped",
    speed: 0,
    direction: "east",
  },
];

export default function TestMapPage() {
  const { isRTL } = useLanguage();
  const [showAirports, setShowAirports] = useState(true);
  const [showSeaports, setShowSeaports] = useState(true);
  const [showVehicles, setShowVehicles] = useState(true);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {isRTL ? "اختبار الخريطة التفاعلية" : "Interactive Map Test"}
          </h1>
          <p className="text-gray-600">
            {isRTL 
              ? "اختبار الخريطة مع الألوان الجديدة والتصميم المستجيب"
              : "Testing the map with new colors and responsive design"
            }
          </p>
        </div>

        {/* Controls */}
        <div className="mb-6 bg-white rounded-lg shadow-sm p-4">
          <h2 className="text-lg font-semibold mb-4">
            {isRTL ? "عناصر التحكم" : "Controls"}
          </h2>
          <div className="flex flex-wrap gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={showAirports}
                onChange={(e) => setShowAirports(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span>{isRTL ? "المطارات" : "Airports"}</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={showSeaports}
                onChange={(e) => setShowSeaports(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span>{isRTL ? "الموانئ البحرية" : "Seaports"}</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={showVehicles}
                onChange={(e) => setShowVehicles(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span>{isRTL ? "المركبات" : "Vehicles"}</span>
            </label>
          </div>
        </div>

        {/* Map Container */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <InteractiveMap
            airports={showAirports ? sampleAirports : []}
            seaports={showSeaports ? sampleSeaports : []}
            vehicles={showVehicles ? sampleVehicles : []}
            center={[24.7136, 46.6753]} // Riyadh coordinates
            zoom={6}
            height="70vh"
            showAirports={showAirports}
            showSeaports={showSeaports}
            showVehicles={showVehicles}
            onAirportClick={(airport) => {
              console.log("Airport clicked:", airport);
              alert(`${isRTL ? "تم النقر على المطار:" : "Airport clicked:"} ${isRTL ? airport.nameAr : airport.name}`);
            }}
            onSeaportClick={(seaport) => {
              console.log("Seaport clicked:", seaport);
              alert(`${isRTL ? "تم النقر على الميناء:" : "Seaport clicked:"} ${isRTL ? seaport.nameAr : seaport.name}`);
            }}
            onVehicleClick={(vehicle) => {
              console.log("Vehicle clicked:", vehicle);
              alert(`${isRTL ? "تم النقر على المركبة:" : "Vehicle clicked:"} ${isRTL ? vehicle.plateNumberAr : vehicle.plateNumber}`);
            }}
          />
        </div>

        {/* Info */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">
            {isRTL ? "معلومات الاختبار" : "Test Information"}
          </h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>
              {isRTL 
                ? "• الخريطة تستخدم الآن ألوان ZATCA الجديدة"
                : "• Map now uses new ZATCA color scheme"
              }
            </li>
            <li>
              {isRTL
                ? "• التصميم مستجيب لجميع أحجام الشاشات"
                : "• Responsive design for all screen sizes"
              }
            </li>
            <li>
              {isRTL
                ? "• تحسينات في الأداء والتفاعل"
                : "• Performance and interaction improvements"
              }
            </li>
            <li>
              {isRTL
                ? "• دعم أفضل للأجهزة المحمولة"
                : "• Better mobile device support"
              }
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
